/**
 * MiChat 主进程入口文件
 *
 * 功能说明:
 * - 管理应用生命周期
 * - 创建和管理主窗口
 * - 处理自动更新逻辑
 * - 管理缓存和用户数据
 * - 处理进程间通信(IPC)
 * - 全局错误处理和日志记录
 *
 * <AUTHOR> <<EMAIL>>
 * @version 5.0.0
 */

const { app, Menu, BrowserWindow, dialog, ipcMain } = require('electron')
const path = require('node:path')
const log = require('electron-log')
const { autoUpdater } = require('electron-updater')
const fs = require('fs')

// ==================== 日志配置 ====================
/**
 * 配置应用日志系统
 * 日志文件位置: %APPDATA%/MiChat/logs/
 */
log.transports.file.level = 'info'
log.info('应用启动')

// ==================== 环境检测 ====================
/**
 * 检查是否是开发环境
 * 开发环境下会启用开发者工具，禁用自动更新
 */
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

// ==================== 目录初始化 ====================
/**
 * 设置应用数据路径并确保目录存在
 * Windows: %APPDATA%/MiChat/
 * macOS: ~/Library/Application Support/MiChat/
 * Linux: ~/.config/MiChat/
 */
const userDataPath = path.join(app.getPath('appData'), app.getName())
try {
  fs.mkdirSync(userDataPath, { recursive: true })
  app.setPath('userData', userDataPath)
  log.info('用户数据目录创建成功:', userDataPath)
} catch (err) {
  log.error('创建用户数据目录失败:', err)
}

/**
 * 创建缓存目录
 * 用于存储会话缓存、临时文件等
 */
const cacheDir = path.join(userDataPath, 'Cache')
try {
  fs.mkdirSync(cacheDir, { recursive: true })
  log.info('缓存目录创建成功:', cacheDir)
} catch (err) {
  log.error('创建缓存目录失败:', err)
}

// ==================== 自动更新系统 ====================
/**
 * 配置自动更新功能
 *
 * 更新流程:
 * 1. 每小时检查一次更新
 * 2. 发现更新时自动下载
 * 3. 下载完成后提示用户安装
 * 4. 支持静默更新和手动更新
 *
 * 更新源: GitHub Releases
 * 更新策略: 自动下载，用户确认安装
 */
function setupAutoUpdater() {
  // 配置更新器日志
  autoUpdater.logger = log
  autoUpdater.logger.transports.file.level = 'info'

  // 自动下载更新，但不自动安装
  autoUpdater.autoDownload = true
  autoUpdater.autoInstallOnAppQuit = true

  /**
   * 更新错误处理
   * 常见错误: 网络连接失败、签名验证失败、权限不足
   */
  autoUpdater.on('error', (error) => {
    log.error('更新出错：', error)
    // 不向用户显示技术错误，避免困扰
  })

  /**
   * 开始检查更新
   */
  autoUpdater.on('checking-for-update', () => {
    log.info('正在检查更新...')
  })

  /**
   * 发现可用更新
   * @param {Object} info - 更新信息，包含版本号、发布说明等
   */
  autoUpdater.on('update-available', (info) => {
    log.info('有可用更新:', info)
    dialog.showMessageBox({
      type: 'info',
      title: '发现新版本',
      message: `发现新版本: ${info.version}`,
      detail: '正在自动下载更新...',
      buttons: ['确定']
    })
  })

  /**
   * 当前已是最新版本
   */
  autoUpdater.on('update-not-available', () => {
    log.info('当前已是最新版本')
  })

  /**
   * 更新下载进度
   * @param {Object} progressObj - 下载进度信息
   */
  autoUpdater.on('download-progress', (progressObj) => {
    let message = `下载速度: ${progressObj.bytesPerSecond}`
    message += `\n已下载 ${progressObj.percent}%`
    message += `\n(${progressObj.transferred}/${progressObj.total})`
    log.info(message)
  })

  /**
   * 更新下载完成，询问用户是否立即安装
   * @param {Object} info - 更新信息
   */
  autoUpdater.on('update-downloaded', (info) => {
    log.info('更新下载完成')
    dialog.showMessageBox({
      type: 'info',
      title: '安装更新',
      message: '更新已下载完成，是否现在安装？',
      buttons: ['现在安装', '稍后安装']
    }).then((result) => {
      if (result.response === 0) {
        // 退出应用并安装更新
        autoUpdater.quitAndInstall(false, true)
      }
    })
  })

  /**
   * 定时检查更新 - 每小时检查一次
   * 避免频繁检查影响性能和网络
   */
  setInterval(() => {
    autoUpdater.checkForUpdates().catch(err => log.error('自动检查更新失败:', err))
  }, 60 * 60 * 1000) // 1小时 = 60分钟 * 60秒 * 1000毫秒

  // 应用启动时立即检查一次更新
  autoUpdater.checkForUpdates().catch(err => log.error('初始检查更新失败:', err))
}

// ==================== 主窗口创建 ====================
/**
 * 创建应用主窗口
 *
 * 安全配置说明:
 * - nodeIntegration: false - 禁用Node.js集成，提高安全性
 * - contextIsolation: true - 启用上下文隔离，防止渲染进程访问主进程
 * - preload: 预加载脚本，安全地暴露主进程功能给渲染进程
 * - partition: 持久化会话，保持登录状态和缓存
 * - disableHardwareAcceleration: 禁用硬件加速，提高兼容性
 *
 * @returns {BrowserWindow} 主窗口实例
 */
const createWindow = () => {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    // 窗口图标配置
    icon: path.join(__dirname, 'build/icons/icon.png'),
    // Web安全配置
    webPreferences: {
      // 安全设置 - 禁用Node.js集成
      nodeIntegration: false,
      // 安全设置 - 启用上下文隔离
      contextIsolation: true,
      // 预加载脚本 - 安全地暴露API
      preload: path.join(__dirname, 'preload.js'),
      // 会话分区 - 持久化用户数据
      partition: 'persist:main',
      // 性能设置 - 禁用硬件加速（提高兼容性）
      disableHardwareAcceleration: true,
      // 会话配置
      session: {
        cachePath: cacheDir
      }
    }
  })

  /**
   * 配置会话和缓存管理
   * 在加载页面前清理缓存，确保应用状态干净
   */
  const ses = mainWindow.webContents.session
  ses.clearCache()
    .then(() => {
      log.info('缓存清理成功')
      // 加载主界面 - 聊天模式
      mainWindow.loadFile('src/chat/index.html')
    })
    .catch(err => {
      log.error('清理缓存失败:', err)
      // 即使清理失败也要加载页面，保证应用可用性
      mainWindow.loadFile('src/chat/index.html')
    })

  /**
   * 隐藏默认菜单栏
   * 提供更简洁的用户界面
   */
  Menu.setApplicationMenu(null)

  /**
   * 窗口崩溃处理
   * 当渲染进程崩溃时，提示用户重启应用
   */
  mainWindow.webContents.on('crashed', () => {
    log.error('窗口崩溃!')
    dialog.showErrorBox('错误', '应用发生错误，需要重启。')
  })

  /**
   * 开发环境配置
   * 自动打开开发者工具，便于调试
   */
  if (isDev) {
    mainWindow.webContents.openDevTools()
    log.info('开发模式：已打开开发者工具')
  }

  return mainWindow
}

// ==================== 应用启动配置 ====================
/**
 * Electron 命令行参数配置
 * 这些参数用于优化应用性能和兼容性
 */
app.commandLine.appendSwitch('no-sandbox')                    // 禁用沙盒模式
app.commandLine.appendSwitch('disable-gpu')                   // 禁用GPU加速
app.commandLine.appendSwitch('disable-software-rasterizer')   // 禁用软件光栅化
app.disableHardwareAcceleration()                            // 全局禁用硬件加速

// ==================== 应用生命周期管理 ====================
/**
 * 应用就绪事件处理
 * 当 Electron 完成初始化并准备创建浏览器窗口时触发
 */
app.whenReady().then(() => {
  // 设置应用名称
  app.name = 'MiChat'
  log.info('应用就绪，开始创建主窗口')

  // 创建主窗口
  const mainWindow = createWindow()

  /**
   * 生产环境启用自动更新
   * 开发环境跳过更新检查，避免干扰开发
   */
  if (!isDev) {
    setupAutoUpdater()
    log.info('自动更新系统已启用')
  } else {
    log.info('开发环境：跳过自动更新')
  }

  /**
   * IPC 处理器：手动检查更新
   * 渲染进程可以通过此接口主动触发更新检查
   *
   * @returns {Object} 更新检查结果
   */
  ipcMain.handle('check-for-updates', async () => {
    if (!isDev) {
      try {
        await autoUpdater.checkForUpdates()
        log.info('手动更新检查成功')
        return { success: true }
      } catch (error) {
        log.error('手动检查更新失败:', error)
        return { success: false, error: error.message }
      }
    }
    return { success: false, error: '开发环境不支持更新检查' }
  })

  /**
   * 全局异常处理
   * 捕获未处理的异常，防止应用崩溃
   */
  process.on('uncaughtException', (error) => {
    log.error('未捕获的异常:', error)
    dialog.showErrorBox('错误', '发生未知错误，请重启应用')
  })

  /**
   * macOS 特有行为：点击 Dock 图标重新激活应用
   * 当所有窗口都关闭时，重新创建窗口
   */
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
      log.info('重新激活应用，创建新窗口')
    }
  })
})

/**
 * 窗口关闭事件处理
 * Windows/Linux: 所有窗口关闭时退出应用
 * macOS: 保持应用运行，符合 macOS 应用习惯
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    log.info('所有窗口已关闭，退出应用')
    app.quit()
  }
})

/**
 * 应用退出前的清理工作
 * 清理缓存、保存用户数据、释放资源
 */
app.on('before-quit', () => {
  log.info('应用准备退出，开始清理工作')
  const ses = BrowserWindow.getAllWindows()[0]?.webContents?.session
  if (ses) {
    ses.clearCache()
      .then(() => log.info('退出前缓存清理成功'))
      .catch(err => log.error('退出前缓存清理失败:', err))
  }
})

// ==================== 单实例应用控制 ====================
/**
 * 确保应用只能运行一个实例
 * 防止用户重复启动应用，造成资源浪费和数据冲突
 */
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  // 如果获取锁失败，说明已有实例在运行，直接退出
  log.info('应用已在运行，退出当前实例')
  app.quit()
} else {
  /**
   * 处理第二个实例启动的情况
   * 当用户尝试再次启动应用时，聚焦到现有窗口
   */
  app.on('second-instance', () => {
    log.info('检测到第二个实例启动，聚焦现有窗口')
    const mainWindow = BrowserWindow.getAllWindows()[0]
    if (mainWindow) {
      // 如果窗口被最小化，恢复窗口
      if (mainWindow.isMinimized()) mainWindow.restore()
      // 聚焦到窗口
      mainWindow.focus()
    }
  })
}

// ==================== 进程错误处理 ====================
/**
 * 渲染进程崩溃处理
 * 当网页内容进程崩溃时触发
 *
 * @param {Event} event - 事件对象
 * @param {WebContents} webContents - 崩溃的网页内容
 * @param {Object} details - 崩溃详情
 */
app.on('render-process-gone', (event, webContents, details) => {
  log.error('渲染进程崩溃:', details)
  dialog.showErrorBox('错误', '渲染进程崩溃，请重启应用。')
})

/**
 * GPU进程崩溃处理
 * 当GPU进程崩溃时触发（通常是显卡驱动问题）
 *
 * @param {Event} event - 事件对象
 * @param {boolean} killed - 进程是否被强制终止
 */
app.on('gpu-process-crashed', (event, killed) => {
  log.error('GPU进程崩溃:', { killed })
  dialog.showErrorBox('错误', 'GPU进程崩溃，请重启应用。可能是显卡驱动问题。')
})