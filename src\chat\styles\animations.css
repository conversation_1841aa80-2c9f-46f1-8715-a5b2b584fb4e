@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.page-enter {
    animation: fadeIn 0.8s ease-out forwards;
}

.header-enter {
    animation: slideInFromTop 0.8s ease-out forwards;
    animation-delay: 0.2s;
    opacity: 0;
}

.content-enter {
    animation: slideInFromBottom 0.8s ease-out forwards;
    animation-delay: 0.4s;
    opacity: 0;
}

.button-enter {
    animation: scaleIn 0.6s ease-out forwards;
    animation-delay: 0.6s;
    opacity: 0;
}

@keyframes messageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.message {
    animation: messageSlideIn 0.5s ease-out forwards;
    opacity: 0;
}

.message:nth-child(n) {
    animation-delay: calc(0.1s * var(--message-index, 0));
}

@keyframes typingEffect {
    from { width: 0 }
    to { width: 100% }
}

.typing-effect {
    overflow: hidden;
    white-space: nowrap;
    animation: typingEffect 2s steps(40, end);
}

@keyframes pulseEffect {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulseEffect 2s infinite ease-in-out;
}

@keyframes typing {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingPulse {
    0% {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }
    50% {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }
}

@keyframes typingBounce {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-4px);
    }
}

@keyframes copiedPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes shimmerOverlay {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes sendPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes pulseDouble {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

.message-typing {
    animation: typing 0.3s ease-out forwards;
}

.message-loading {
    animation: typingPulse 1.5s infinite;
}

.message-send {
    animation: sendPulse 0.3s ease-out;
}

.message-copy {
    animation: copiedPulse 0.3s ease-out;
}

.message-shimmer {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes waveform {
    0%, 100% {
        transform: scaleY(0.2);
        opacity: 0.5;
    }
    50% {
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes modalSlideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
