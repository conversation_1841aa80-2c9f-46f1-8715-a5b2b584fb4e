.modal {
    display: none;
    position: fixed;
    z-index: 9999999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    flex-direction: column;
    align-items: center;
}

.modal-content {
    position: relative;
    background: white;
    margin-top: 5%;
    padding: 30px;
    width: 90%;
    max-width: 400px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
    animation: modalSlideDown 0.3s ease;
}

#modal-message {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    line-height: 1.6;
    font-weight: 500;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.confirm-btn {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
}

.confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.cancel-btn {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
    backdrop-filter: blur(4px);
}

.cancel-btn:hover {
    background: rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* 应用选择弹窗样式 */
.app-select-content {
    margin: 10% auto !important;
    max-width: 500px !important;
    padding: 20px !important;
}

.app-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
    margin-top: 10px;
}

.app-item {
    background: linear-gradient(145deg, #ffffff, #f8f9ff);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;
}

/* 主应用样式 */
.app-item.main-app {
    background: linear-gradient(145deg, #f0f7ff, #e6f3ff);
    border: 2px solid var(--primary-color);
    position: relative;
}

/* 主应用标识徽章 */
.app-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-item h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.app-item .app-url {
    font-size: 0.85rem;
    color: #666;
    word-break: break-all;
    opacity: 0.8;
}

.app-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: var(--primary-color);
}

/* 滚动条样式 */
.app-list::-webkit-scrollbar {
    width: 6px;
}

.app-list::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
}

.app-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.app-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 当鼠标悬停在应用列表上时显示滚动条 */
.app-list:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
}

/* 优化应用列表在有滚动条时的内边距 */
.app-list {
    padding-right: 15px;
}