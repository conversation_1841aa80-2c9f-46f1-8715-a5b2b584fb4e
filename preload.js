/**
 * MiChat 预加载脚本 (Preload Script)
 *
 * 功能说明:
 * - 在渲染进程中安全地暴露主进程功能
 * - 作为主进程和渲染进程之间的安全桥梁
 * - 提供受限的 Node.js API 访问
 * - 实现进程间通信 (IPC) 的安全封装
 *
 * 安全特性:
 * - 运行在独立的上下文中，与网页内容隔离
 * - 只能访问预定义的 API，不能直接访问 Node.js
 * - 通过 contextBridge 安全地暴露功能给渲染进程
 *
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @version 5.0.0
 */

const { contextBridge, ipcRenderer } = require('electron')

// ==================== API 暴露 ====================
/**
 * 通过 contextBridge 安全地向渲染进程暴露主进程功能
 *
 * 安全说明:
 * - contextBridge 确保 API 在隔离的上下文中运行
 * - 渲染进程只能访问这里明确暴露的功能
 * - 防止渲染进程直接访问 Node.js API，提高安全性
 */
contextBridge.exposeInMainWorld('electronAPI', {
    /**
     * 检查应用更新
     *
     * @returns {Promise<Object>} 更新检查结果
     * @example
     * // 在渲染进程中调用
     * const result = await window.electronAPI.checkForUpdates()
     * if (result.success) {
     *   console.log('更新检查成功')
     * } else {
     *   console.error('更新检查失败:', result.error)
     * }
     */
    checkForUpdates: () => ipcRenderer.invoke('check-for-updates')
})

// ==================== DOM 就绪处理 ====================
/**
 * DOM 内容加载完成后的初始化处理
 * 主要用于显示应用版本信息和环境信息
 */
window.addEventListener('DOMContentLoaded', () => {
    /**
     * 替换页面中的版本信息文本
     *
     * @param {string} selector - 元素选择器
     * @param {string} text - 要设置的文本内容
     */
    const replaceText = (selector, text) => {
      const element = document.getElementById(selector)
      if (element) {
        element.innerText = text
      }
    }

    /**
     * 显示运行环境版本信息
     * 包括 Chrome、Node.js、Electron 的版本号
     * 这些信息对调试和技术支持很有用
     */
    for (const dependency of ['chrome', 'node', 'electron']) {
      replaceText(`${dependency}-version`, process.versions[dependency])
    }
})