/**
 * MiChat 主样式文件
 *
 * 样式架构说明:
 * - 采用模块化 CSS 设计，按功能分离样式文件
 * - 使用 CSS 变量实现主题系统
 * - 响应式设计，支持桌面端和移动端
 * - 统一的动画和过渡效果
 *
 * 文件结构:
 * - base.css: 基础样式和 CSS 变量定义
 * - layout.css: 布局相关样式
 * - messages.css: 消息和聊天界面样式
 * - settings.css: 设置页面样式
 * - voice.css: 语音功能相关样式
 * - animations.css: 动画效果定义
 * - themes.css: 主题色彩系统
 * - modal.css: 弹窗和模态框样式
 *
 * <AUTHOR> Chaojin <<EMAIL>>
 * @version 5.0.0
 */

/* ==================== 基础样式 ==================== */
@import 'base.css';

/* ==================== 布局样式 ==================== */
@import 'layout.css';

/* ==================== 消息样式 ==================== */
@import 'messages.css';

/* ==================== 设置样式 ==================== */
@import 'settings.css';

/* ==================== 语音样式 ==================== */
@import 'voice.css';

/* ==================== 动画效果 ==================== */
@import 'animations.css';

/* ==================== 主题系统 ==================== */
@import 'themes.css';

/* ==================== 弹窗样式 ==================== */
@import 'modal.css';