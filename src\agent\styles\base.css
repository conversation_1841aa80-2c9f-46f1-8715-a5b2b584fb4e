/* 基础样式设置 */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --bg-color: #ffffff;
    --text-color: #333333;
    --border-color: #e5e5e5;
    --avatar-size: 40px;
    --avatar-margin: 12px;
    --message-radius: 12px;
    --header-height: 60px;
    --sidebar-width: 280px;
    --animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --user-message-bg: #e3f2fd;
    --bot-message-bg: #f5f5f5;
    --input-bg: #f8f9fa;
    --input-border: #e5e5e5;
    --input-bg-focus: rgba(255, 255, 255, 0.95);
    --input-focus-border: #4a90e2;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    overflow: hidden;
}

button {
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    font: inherit;
    color: inherit;
    outline: none;
}

textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    border: none;
    outline: none;
    resize: none;
    width: 100%;
    padding: 12px;
    background: transparent;
    flex: 1;
    min-height: 47px;
    max-height: 200px;
    border-radius: 0.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
    transition: all 0.2s ease;
    background: var(--input-bg);
    border: 1px solid var(--input-border);
}

textarea:focus {
    background: var(--input-bg-focus, rgba(255, 255, 255, 0.95));
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    border-color: var(--input-focus-border);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #666;
}

/* 工具栏按钮样式 */
.menu-button,
.show-sidebar-button {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: var(--bg-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    display: none;
    color: var(--text-color);
}

.show-sidebar-button {
    display: none;
}

/* 遮罩层样式 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.overlay.active {
    display: block;
}

.chat-mode-button {
    position: fixed;
    top: 84%;
    right: 20px;
    z-index: 1000;
    background-color: #4a4a4a;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.chat-mode-button:hover {
    background-color: #333;
    transform: translateY(-2px);
}

.chat-mode-button i {
    font-size: 16px;
}