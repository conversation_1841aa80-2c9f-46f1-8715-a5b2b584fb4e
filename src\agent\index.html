<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度思考助手 - 多轮思考模式</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="icon" href="../../favicon.png" type="image/png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/styles/googlecode.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>

<body class="theme-light">
    <button id="chatModeButton" class="chat-mode-button" onclick="window.location.href='../chat/index.html'">
        <i class="fas fa-comment"></i>
        返回普通对话
    </button>

    <button id="menuButton" class="menu-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <button id="showSidebarButton" class="show-sidebar-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <div id="overlay" class="overlay"></div>

    <div id="tipPage" class="modal">
        <div class="modal-content">
            <p id="modal-message"></p>
            <div class="modal-buttons">
                <input type="button" id="confirmBtn" class="btn confirm-btn" value="确定">
                <input type="button" id="cancelBtn" class="btn cancel-btn" value="取消">
            </div>
        </div>
    </div>

    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <button id="newChatButton" class="new-chat-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                        <path fill="none" d="M0 0h24v24H0z" />
                        <path fill="currentColor" d="M11 11V5h2v6h6v2h-6v6h-2v-6H5v-2z" />
                    </svg>
                    新对话
                </button>
                <button id="toggleSidebarButton" class="toggle-sidebar-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                        <path fill="currentColor" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
                    </svg>
                </button>
            </div>
            <div class="conversation-list" id="conversationList">
                <div class="conversation-header">思考历程</div>
                <div class="conversation-items" id="conversationItems">
                    <!-- 对话列表将在这里动态添加 -->
                </div>
            </div>
            <div class="user-info">
                <img id="userAvatar" class="user-avatar" src="https://api.dicebear.com/7.x/adventurer/svg?seed=agent"
                    alt="用户头像">
                <span id="userName" class="user-name">思考助手</span>
                <button id="settingsButton" class="settings-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                        <path fill="currentColor"
                            d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
                    </svg>
                </button>
            </div>
        </div>
        <div class="main-content">
            <div id="welcomePage" class="welcome-page" style="display: flex;">
                <div class="welcome-content">
                    <h1>深度思考助手</h1>
                    <div class="welcome-suggestions">
                        <div class="suggestions-group">
                            <h2>思考模式</h2>
                            <div class="welcome-suggestion-items">
                                <button>"请帮我分析这个问题"</button>
                                <button>"让我们深入探讨"</button>
                                <button>"从多个角度思考"</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>思维方式</h2>
                            <div class="welcome-suggestion-items">
                                <button>系统思维</button>
                                <button>批判性思维</button>
                                <button>创造性思维</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>思考层次</h2>
                            <div class="welcome-suggestion-items">
                                <button>表层分析</button>
                                <button>深度剖析</button>
                                <button>本质探究</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>思考方法</h2>
                            <div class="welcome-suggestion-items">
                                <button>演绎推理</button>
                                <button>归纳总结</button>
                                <button>类比思考</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="welcome-input-container">
                    <div class="input-group">
                        <textarea id="welcomeUserInput" placeholder="请输入您想深入思考的问题..." rows="1" style="height: 47px;"></textarea>
                        <button id="welcomeSendButton">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z" />
                                <path fill="currentColor"
                                    d="M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.91L2 9.12c0 .**********.99L17 12 2.87 13.88c-.5.07-.87.5-.87 1l.01 4.61c0 .71.73 1.2 1.39.91z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div id="chatContainer" class="chat-container" style="display: none;">
                <div class="chat-messages" id="chatMessages"></div>
                <div class="input-container">
                    <div class="input-group">
                        <textarea id="userInput" placeholder="请输入您想深入思考的问题..." rows="1" style="height: 47px;"></textarea>
                        <button id="sendButton">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z" />
                                <path fill="currentColor"
                                    d="M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.91L2 9.12c0 .**********.99L17 12 2.87 13.88c-.5.07-.87.5-.87 1l.01 4.61c0 .71.73 1.2 1.39.91z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <script>
        // 初始化全局实例
        const agentChat = new AgentChat();
        window.agentChatInstance = agentChat;
    </script>
</body>

</html>