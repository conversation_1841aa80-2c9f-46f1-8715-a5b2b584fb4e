<!DOCTYPE html>
<html lang="zh">

<head>
    <!-- ==================== 基础元信息 ==================== -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DifyWebUI - 智能对话助手</title>

    <!-- ==================== 样式资源 ==================== -->
    <!-- 主样式文件 - 包含所有自定义样式 -->
    <link rel="stylesheet" href="styles/main.css">

    <!-- 应用图标 -->
    <link rel="icon" href="../../favicon.png" type="image/png">

    <!-- 外部样式库 -->
    <!-- Font Awesome 图标库 - 提供丰富的图标支持 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Highlight.js 代码高亮样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/styles/googlecode.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css">

    <!-- 代码字体 - Fira Code 等宽字体，适合代码显示 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap">

    <!-- ==================== JavaScript 库 ==================== -->
    <!-- Highlight.js - 代码语法高亮库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>

    <!-- Marked.js - Markdown 解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>

<body>
    <!-- ==================== 移动端导航控制 ==================== -->
    <!-- 汉堡菜单按钮 - 用于移动端显示/隐藏侧边栏 -->
    <button id="menuButton" class="menu-button" title="打开菜单">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <!-- 显示侧边栏按钮 - 当侧边栏隐藏时显示 -->
    <button id="showSidebarButton" class="show-sidebar-button" title="显示侧边栏">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <!-- 遮罩层 - 用于移动端侧边栏显示时的背景遮罩 -->
    <div id="overlay" class="overlay"></div>

    <div id="tipPage" class="modal">
        <div class="modal-content">
            <p id="modal-message"></p>
            <div class="modal-buttons">
                <input type="button" id="confirmBtn" class="btn confirm-btn" value="确定">
                <input type="button" id="cancelBtn" class="btn cancel-btn" value="取消">
            </div>
        </div>
    </div>

    <!-- 在 tipPage 后面添加应用选择弹窗 -->
    <div id="appSelectModal" class="modal">
        <div class="modal-content app-select-content">
            <h3>选择应用</h3>
            <div id="appList" class="app-list">
            </div>
        </div>
    </div>

    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <button id="newChatButton" class="new-chat-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                        <path fill="none" d="M0 0h24v24H0z" />
                        <path fill="currentColor" d="M11 11V5h2v6h6v2h-6v6h-2v-6H5v-2z" />
                    </svg>
                    新对话
                </button>
                <button id="toggleSidebarButton" class="toggle-sidebar-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="conversation-list" id="conversationList">
                <div class="conversation-header">历史对话</div>
                <div class="conversation-items" id="conversationItems">
                    <!-- 对话列表将在这里动态添加 -->
                </div>
            </div>
            <div class="user-info">
                <img id="userAvatar" class="user-avatar" src="https://api.dicebear.com/7.x/adventurer/svg?seed=user"
                    alt="用户头像">
                <span id="userName" class="user-name">未设置用户名</span>
                <button id="settingsButton" class="settings-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                        <path fill="currentColor"
                            d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.07.62-.07.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
                    </svg>
                </button>
            </div>
        </div>
        <div class="main-content">
            <div id="welcomePage" class="welcome-page">
                <div class="welcome-content">
                    <h1>今天有什么能帮助你的呢？</h1>
                    <div class="welcome-suggestions">
                        <div class="suggestions-group">
                            <h2>代码示例</h2>
                            <div class="welcome-suggestion-items">
                                <button>"写一个快速排序算法"</button>
                                <button>"解释设计模式中的工厂模式"</button>
                                <button>"如何解决JavaScript中的内存泄漏"</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>开发工具</h2>
                            <div class="welcome-suggestion-items">
                                <button>Git常用命令</button>
                                <button>Docker基础教程</button>
                                <button>VS Code快捷键</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>框架学习</h2>
                            <div class="welcome-suggestion-items">
                                <button>React基础入门</button>
                                <button>Vue3新特性介绍</button>
                                <button>SpringBoot实战</button>
                            </div>
                        </div>
                        <div class="suggestions-group">
                            <h2>数据库</h2>
                            <div class="welcome-suggestion-items">
                                <button>MySQL性能优化</button>
                                <button>Redis缓存策略</button>
                                <button>MongoDB最佳实践</button>
                            </div>
                        </div>
                    </div>
                    <div class="welcome-input-container">
                        <div class="input-container">
                            <div class="current-app-tag">
                                <svg class="app-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                                    <path fill="none" d="M0 0h24v24H0z"/>
                                    <path fill="currentColor" d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 8H4v8h16v-8zm0-2V5H4v4h16z"/>
                                </svg>
                                <span class="app-name">当前应用名称</span>
                                <span class="switch-app">切换</span>
                                <span class="think-mode" id="think-mode" onclick="window.location.href='../agent/index.html'">思考模式</span>
                                <span class="stop-responses" id="stop-responses" style="display: none;">停止回复</span>
                            </div>
                            <div class="attachment-preview" id="welcomeAttachmentPreview"></div>
                            <div class="input-group">
                                <button id="welcomeUploadButton" class="upload-button">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                        <path fill="none" d="M0 0h24v24H0z" />
                                        <path fill="currentColor"
                                            d="M21 15v3h3v2h-3v3h-2v-3h-3v-2h3v-3h2zm-10 3H5v-3H3v3a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-3h-2v3zm-7-9h12V7H4v2zm14-6H4a2 2 0 0 0-2 2h16a2 2 0 0 0-2-2z" />
                                    </svg>
                                </button>
                                <input type="file" id="welcomeFileInput"
                                    accept="image/png,image/jpeg,image/jpg,image/webp,image/gif" multiple style="display: none">

                                <textarea id="welcomeUserInput" placeholder="输入消息..." rows="1"></textarea>
                                <button id="welcomeSendButton">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                        <path fill="none" d="M0 0h24v24H0z" />
                                        <path fill="currentColor"
                                            d="M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.91L2 9.12c0 .**********.99L17 12 2.87 13.88c-.5.07-.87.5-.87 1l.01 4.61c0 .71.73 1.2 1.39.91z" />
                                    </svg>
                                </button>
                                <button id="micPButton" class="mic-p-button">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                        <path fill="none" d="M0 0h24v24H0z"/>
                                        <path fill="currentColor" d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="chatContainer" class="chat-container" style="display: none;">
                <div class="chat-messages" id="chatMessages"></div>
                <div class="input-container">
                    <div class="current-app-tag">
                        <svg class="app-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16">
                            <path fill="none" d="M0 0h24v24H0z"/>
                            <path fill="currentColor" d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 8H4v8h16v-8zm0-2V5H4v4h16z"/>
                        </svg>
                        <span class="app-name">当前应用名称</span>
                        <span class="switch-app">切换</span>
                        <span class="think-mode" id="chat-think-mode" onclick="window.location.href='../agent/index.html'">思考模式</span>
                        <span class="stop-responses" id="stop-responses" style="display: none;">停止回复</span>
                    </div>
                    <div class="attachment-preview" id="attachmentPreview"></div>
                    <div class="input-group">
                        <button id="uploadButton" class="upload-button">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z" />
                                <path fill="currentColor"
                                    d="M21 15v3h3v2h-3v3h-2v-3h-3v-2h3v-3h2zm-10 3H5v-3H3v3a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-3h-2v3zm-7-9h12V7H4v2zm14-6H4a2 2 0 0 0-2 2h16a2 2 0 0 0-2-2z" />
                            </svg>
                        </button>
                        <input type="file" id="fileInput" accept="image/png,image/jpeg,image/jpg,image/webp,image/gif"
                           multiple style="display: none">
                        <textarea id="userInput" placeholder="输入消息..." rows="1"></textarea>
                        <button id="sendButton">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z" />
                                <path fill="currentColor"
                                    d="M3.4 20.4l17.45-7.48c.81-.35.81-1.49 0-1.84L3.4 3.6c-.66-.29-1.39.2-1.39.91L2 9.12c0 .**********.99L17 12 2.87 13.88c-.5.07-.87.5-.87 1l.01 4.61c0 .71.73 1.2 1.39.91z" />
                            </svg>
                        </button>
                        <button id="micPButton" class="mic-p-button">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path fill="none" d="M0 0h24v24H0z"/>
                                <path fill="currentColor" d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div id="voiceContainer" class="voice-container" style="display: none;">
                <div class="chat-body" id="chatBody">
                    <div class="mic-button" id="micButton">
                        <svg class="mic-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                        </svg>
                    </div>
                    <div class="wave-container" id="waveContainer" style="display: none;">
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                        <div class="wave"></div>
                    </div>
                    <div class="status-text" id="statusText">
                        点击麦克风开始对话
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="settingsPage" class="settings-page" style="display: none;">
                <button class="close-settings" id="closeSettings">x</button>
                <button class="settings-nav-button prev" id="prevButton">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="settings-nav-button next" id="nextButton">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <div class="settings-container" id="settingsContainer">
                    <div class="settings-wrapper" id="settingsWrapper">
                        <div class="settings-content main-settings">
                            <div class="app-title">
                                <h3>主应用设置</h3>
                            </div>
                            <form id="settingsForm" class="settings-form">
                                <div class="form-group">
                                    <label for="apiKey">API Key</label>
                                    <input type="text" id="apiKey" name="apiKey" required>
                                </div>
                                <div class="form-group">
                                    <label for="baseUrl">Base URL</label>
                                    <input type="url" id="baseUrl" name="baseUrl" required>
                                </div>
                                <div class="form-group">
                                    <label for="userId">User ID</label>
                                    <input type="text" id="userId" name="userId" required>
                                </div>
                                <div class="form-group">
                                    <label for="userName">用户名</label>
                                    <input type="text" id="userNameInput" name="userName" required>
                                </div>
                                <div class="form-group">
                                    <label>主题色</label>
                                    <div class="theme-selector">
                                        <div class="theme-option active" data-theme="default"
                                            style="background-color: #111111"></div>
                                            <div class="theme-option" data-theme="blue" style="background: linear-gradient(135deg, #2d7fea, rgba(45, 127, 250, 0.7));"></div>
                                            <div class="theme-option" data-theme="purple" style="background: linear-gradient(135deg, #7c3aed, rgba(124, 58, 237, 0.7));"></div>
                                            <div class="theme-option" data-theme="red" style="background: linear-gradient(135deg, #ef4444, rgba(239, 68, 68, 0.7));"></div>
                                            <div class="theme-option" data-theme="orange" style="background: linear-gradient(135deg, #f97316, rgba(249, 115, 22, 0.7));"></div>
                                            <div class="theme-option" data-theme="green" style="background: linear-gradient(135deg, #10b981, rgba(16, 185, 129, 0.7));"></div>
                                            <div class="theme-option" data-theme="pink" style="background: linear-gradient(135deg, #ec4899, rgba(236, 72, 153, 0.7));"></div>
                                            <div class="theme-option" data-theme="yellow" style="background: linear-gradient(135deg, #eab308, rgba(234, 179, 8, 0.7));"></div>
                                            <div class="theme-option" data-theme="indigo" style="background: linear-gradient(135deg, #6366f1, rgba(99, 102, 241, 0.7));"></div>
                                            <div class="theme-option" data-theme="cyan" style="background: linear-gradient(135deg, #06b6d4, rgba(6, 182, 212, 0.7));"></div>
                                            <div class="theme-option" data-theme="sky" style="background: linear-gradient(135deg, #0ea5e9, rgba(14, 165, 233, 0.7));"></div>
                                            <div class="theme-option" data-theme="lime" style="background: linear-gradient(135deg, #84cc16, rgba(132, 204, 22, 0.7));"></div>
                                            <div class="theme-option" data-theme="amber" style="background: linear-gradient(135deg, #f59e0b, rgba(245, 158, 11, 0.7));"></div>
                                            <div class="theme-option" data-theme="emerald" style="background: linear-gradient(135deg, #10b981, rgba(16, 185, 129, 0.7));"></div>
                                            <div class="theme-option" data-theme="rose" style="background: linear-gradient(135deg, #f43f5e, rgba(244, 63, 94, 0.7));"></div>
                                            <div class="theme-option" data-theme="fuchsia" style="background: linear-gradient(135deg, #d946ef, rgba(217, 70, 239, 0.7));"></div>                                        
                                    </div>
                                </div>
                                <button type="submit" class="save-button" style="display: inline-block; margin-right: 10px;">保存设置</button>
                                <button type="button" id="addAppButton" class="save-button" style="display: inline-block;">添加应用</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加必要的库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script src="script.js"></script>
</body>

</html>
