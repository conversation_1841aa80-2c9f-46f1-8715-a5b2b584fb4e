.app-container {
    display: flex;
    height: 95vh;
    width: 100%;
}

.conversation-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.conversation-header {
    padding: 0.75rem 1rem;
    font-weight: bold;
    border-bottom: none;
    font-size: 0.95rem;
}

.conversation-items {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.conversation-item {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    margin-bottom: 0.5rem;
    transition: background-color 0.2s;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: transparent;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    line-height: 1.4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    gap: 8px;
}

.sidebar {
    width: var(--sidebar-width);
    background: #ffffff;
    border-right: none;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    transition: transform 0.3s var(--animation-timing-function);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    transform: translateX(calc(-1 * var(--sidebar-width)));
}

.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    transition: margin-left 0.3s var(--animation-timing-function);
    min-width: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-content.sidebar-collapsed {
    margin-left: 0;
}

.chat-container {
    flex: 1;
    max-width: none;
    margin: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    position: relative;
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #ffffff;
    margin-bottom: calc(var(--header-height) + 2rem);
    padding-bottom: 4rem;
}

.input-container {
    position: fixed;
    bottom: 0;
    left: var(--sidebar-width);
    right: 0;
    width: auto;
    border: none;
    border-radius: 0.75rem 0.75rem 0 0;
    background: #ffffff;
    box-shadow: none;
    padding: 1rem;
    z-index: 100;
    transition: left 0.3s var(--animation-timing-function);
}

.input-group {
    display: flex;
    gap: 0.5rem;
    width: 100%;
}

.welcome-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem;
    background: #ffffff;
}

.welcome-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.think-mode-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem;
    background: #ffffff;
}

.think-mode-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.think-mode-content h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
    text-align: center;
}

.think-mode-workspace {
    width: 100%;
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: none;
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(calc(-1 * var(--sidebar-width)));
    }

    .sidebar.mobile-visible {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .menu-button {
        display: flex;
    }

    .welcome-suggestions {
        flex-direction: column;
        padding: 0 1rem;
    }

    .suggestions-group {
        min-width: 100%;
    }

    .welcome-input-container {
        padding: 0 1rem;
    }

    .input-container {
        left: 0;
    }
}

textarea {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 0.5rem;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    outline: none;
    min-height: 20px;
    max-height: 200px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    box-shadow: none;
    transition: all 0.3s ease;
}

textarea:focus {
    border-color: transparent;
    box-shadow: none;
}

/* 滚动条样式 */
.conversation-items::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.conversation-items::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.conversation-items::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

/* 文本框滚动条样式 */
textarea::-webkit-scrollbar {
    width: 4px;
}

textarea::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

textarea::-webkit-scrollbar-track {
    background: transparent;
}

/* 加载状态样式 */
.loading,
.error,
.no-conversations {
    padding: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.error {
    color: #ff4d4f;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.loading:after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid #999;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.conversation-content {
    flex: 1;
    min-width: 0;
    cursor: pointer;
}

.conversation-content > div:first-child {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.conversation-item:hover {
    background: #f8f8f8;
}

.conversation-item.active {
    background: #f2f2f2;
    font-weight: 500;
}

.conversation-item.active .time {
    color: rgba(255, 255, 255, 0.8);
}

.conversation-item .time {
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.25rem;
}

.delete-btn {
    padding: 5px;
    background: none;
    border: none;
    color: #ff4d4d;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    flex-shrink: 0;
}

.delete-btn:hover {
    opacity: 1;
}

/* 添加侧边栏按钮样式 */
.sidebar-header {
    padding: 0.75rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;
    border-bottom: none;
}

.new-chat-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.toggle-sidebar-button {
    background: transparent;
    color: var(--text-color);
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 欢迎页面样式 */
.welcome-suggestions {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.suggestions-group {
    flex: 1;
    min-width: 300px;
}

.suggestions-group h2 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    opacity: 0.8;
}

.welcome-suggestion-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.welcome-suggestion-items button {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e5e5e5;
    color: var(--text-color);
    padding: 1rem;
    border-radius: 0.75rem;
    cursor: pointer;
    backdrop-filter: blur(8px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    font-size: 0.95rem;
    box-shadow: none;
}

.welcome-suggestion-items button:hover {
    background: var(--message-bg);
    border-color: var(--primary-color);
    transform: none;
    box-shadow: none;
}

/* 遮罩层样式 */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.overlay.visible {
    display: block;
}

/* 用户信息样式 */
.user-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding: 1.35rem;
    gap: 0.75rem;
    border-top: none;
    background: var(--message-bg);
    margin-top: auto;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: none;
}

.user-name {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.settings-button {
    background: transparent;
    color: var(--text-color);
    padding: 0.5rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s;
    margin-left: auto;
}

.settings-button:hover {
    background: var(--message-bg);
    opacity: 1;
}

/* 修改sidebar样式，添加flex布局以支持底部固定的用户信息 */
.sidebar {
    display: flex;
    flex-direction: column;
}

.conversation-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 60px; /* 为底部用户信息留出空间 */
}

/* 移动端适配 */
@media (max-width: 768px) {
    .user-info {
        padding: 0.75rem;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
    }

    .user-name {
        font-size: 0.85rem;
    }
}

/* 菜单按钮样式 */
.menu-button {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: none;
    border: none;
    color: white;
}

.show-sidebar-button {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: var(--primary-color);
    border-radius: 50%;
    width: 70px;
    height: 50px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: none;
}

.show-sidebar-button.visible {
    display: flex;
}

/* 修改侧边栏过渡效果 */
.sidebar {
    transition: transform 0.3s var(--animation-timing-function);
}

.main-content {
    transition: margin-left 0.3s var(--animation-timing-function);
}

.input-container {
    transition: left 0.3s var(--animation-timing-function);
}

/* 当侧边栏收起时，输入框应该占据全宽 */
.sidebar-collapsed .input-container {
    left: 0;
}

/* 确保内容区域在侧边栏收起时正确显示 */
.main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
}

/* 修改欢迎页面标题样式 */
.welcome-content h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    display: inline-block;
    text-align: center;
    font-weight: 700;
    letter-spacing: -0.02em;
    line-height: 1.2;
    animation: titleFadeIn 0.8s ease-out, gradientShift 6s ease infinite;
    text-shadow: none;
}

.welcome-content h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--current-theme);
    border-radius: 3px;
    transform: scaleX(0);
    transform-origin: left;
    animation: lineSlideIn 0.6s ease-out forwards 0.4s;
}

/* 添加标题动画 */
@keyframes titleFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes lineSlideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .welcome-content h1 {
        font-size: clamp(1.5rem, 6vw, 2rem);
        margin-bottom: 1.2rem;
    }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 悬停效果 */
.welcome-content h1:hover {
    transform: none;
    transition: transform 0.3s ease;
}

.welcome-content h1:hover::after {
    transform: scaleX(1);
    transition: transform 0.3s ease;
}

/* 添加应用标签样式 */
.current-app-tag {
    position: absolute;
    bottom: calc(100% + 8px);  /* 位于输入框上方8px */
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: none;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10;
    transition: all 0.3s ease;
    white-space: nowrap;
    max-width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    border: none;
}

.current-app-tag:hover {
    transform: translateX(-50%);
    box-shadow: none;
}

.current-app-tag .app-icon {
    width: 16px;
    height: 16px;
    opacity: 0.9;
}

.current-app-tag .switch-app {
    margin-left: 8px;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.current-app-tag .switch-app:hover {
    background: rgba(255, 255, 255, 0.3);
}

.think-mode {
    margin-left: 8px;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.think-mode:hover {
    background: rgba(255, 255, 255, 0.3);
}

.stop-responses {
    margin-left: 8px;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.stop-responses:hover {
    background: red;
}