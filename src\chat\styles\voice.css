.voice-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: fit-content;
    height: fit-content;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    border-radius: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 2rem;
}

.chat-body {
    flex-grow: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    width: fit-content;
    margin: 0;
    background-color: transparent;
}

.mic-button {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(145deg, #11b88e, #0e906f);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 6px 12px rgba(16, 163, 127, 0.3);
}

.mic-button::before,
.mic-button::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(145deg, var(--primary-color), var(--primary-color));
    opacity: 0;
    animation: pulseDouble 3s infinite;
}

.mic-button::after {
    animation-delay: 1s;
}

.mic-button:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 10px 20px rgba(16, 163, 127, 0.4);
}

.mic-button:active {
    transform: scale(0.95);
}

.wave-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    margin-top: 40px;
    gap: 3px;
    perspective: 1000px;
}

.wave {
    width: 4px;
    height: 30px;
    background: linear-gradient(to top, #10a37f, #11b88e);
    border-radius: 4px;
    animation: waveform 1.0s infinite ease-in-out;
    transform-origin: bottom;
    box-shadow: 0 2px 6px rgba(16, 163, 127, 0.2);
}

.wave:nth-child(odd) {
    animation-delay: 0.8s;
}

.wave:nth-child(even) {
    animation-delay: 1.6s;
}

.status-text {
    margin-top: 25px;
    font-size: 1.1em;
    color: #000000;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    letter-spacing: 0.02em;
}

.mic-icon {
    width: 36px;
    height: 36px;
    fill: #ffffff;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 录音状态样式 */
.recording .mic-button {
    background: linear-gradient(145deg, #ff5f5f, #ff4a4a);
    box-shadow: 0 6px 12px rgba(255, 74, 74, 0.3);
}

.recording .mic-button::before,
.recording .mic-button::after {
    background: linear-gradient(145deg, #ff5f5f, #ff4a4a);
}

.recording .wave {
    background: linear-gradient(to top, #ff4a4a, #ff5f5f);
    box-shadow: 0 2px 6px rgba(255, 74, 74, 0.2);
}

/* 机器人回复状态样式 */
.bot-replying .wave {
    background: linear-gradient(to top, #7c3aed, #8b5cf6);
    box-shadow: 0 2px 6px rgba(124, 58, 237, 0.2);
}

.bot-replying .status-text {
    color: #7c3aed;
}

.bot-replying .typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 4px;
    height: 4px;
    background-color: #7c3aed;
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .voice-container {
        width: 90%;
        max-width: 400px;
        padding: 1.5rem;
    }

    .mic-button {
        width: 60px;
        height: 60px;
    }

    .wave-container {
        height: 50px;
        margin-top: 30px;
    }

    .status-text {
        font-size: 1em;
    }
}

// ... 其他语音输入相关样式 ... 