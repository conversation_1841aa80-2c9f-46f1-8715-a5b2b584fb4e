name: Build/release

on:
  push:
    tags:
      - 'v*'

# 添加权限配置
permissions:
  contents: write # 用于创建 release 和上传构建文件
  issues: write
  pull-requests: write

jobs:
  release:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
      fail-fast: false

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |    
            ${{ runner.os }}-node-

      - name: Install Dependencies
        run: npm install

      # Linux 依赖
      - name: Install Linux Dependencies
        if: matrix.os == 'ubuntu-latest'
        run: |  
          sudo dpkg --add-architecture i386
          sudo apt-get update
          sudo apt-get install -y wine64 wine32 rpm
          sudo apt-get install -y --no-install-recommends libopenjp2-tools

      # Windows 构建
      - name: Build/release Windows
        if: matrix.os == 'windows-latest'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npm run publish:win
      # macOS 构建
      - name: Build/release MacOS
        if: matrix.os == 'macos-latest'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npm run publish:mac
      # Linux 构建
      - name: Build/release Linux
        if: matrix.os == 'ubuntu-latest'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npm run publish:linux