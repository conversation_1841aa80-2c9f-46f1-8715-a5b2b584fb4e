/* 页面入场动画 */
.welcome-content {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease forwards;
}

.welcome-suggestions {
    opacity: 0;
    animation: fadeIn 0.8s ease 0.3s forwards;
}

.suggestions-group {
    opacity: 0;
    transform: translateY(10px);
    animation: fadeInUp 0.6s ease forwards;
}

.suggestions-group:nth-child(1) { animation-delay: 0.4s; }
.suggestions-group:nth-child(2) { animation-delay: 0.6s; }
.suggestions-group:nth-child(3) { animation-delay: 0.8s; }
.suggestions-group:nth-child(4) { animation-delay: 1s; }

.welcome-input-container {
    opacity: 0;
    transform: translateY(10px);
    animation: fadeInUp 0.6s ease 1.2s forwards;
}

/* 消息动画 */
.message {
    opacity: 0;
    transform: translateY(10px);
    animation: messageSlideIn 0.4s ease forwards;
}

.message.user {
    animation-name: messageSlideInRight;
}

.message.assistant {
    animation-name: messageSlideInLeft;
}

/* 基础动画关键帧 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes messageSlideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes messageSlideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 交互动画 */
.welcome-suggestion-items button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-suggestion-items button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.input-group textarea,
.input-group button {
    transition: all 0.2s ease;
}

.input-group textarea:focus {
    box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
}

.input-group button:hover {
    transform: scale(1.05);
}

/* 侧边栏动画 */
.sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

/* 模态框动画 */
.modal {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show {
    opacity: 1;
    transform: scale(1);
}

.overlay {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.overlay.show {
    opacity: 1;
}