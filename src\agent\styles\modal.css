/* 通用模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--bg-color);
    border-radius: 12px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideUp 0.3s ease;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.confirm-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.confirm-btn:hover {
    background-color: var(--primary-color-dark);
}

.cancel-btn {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.cancel-btn:hover {
    background-color: var(--hover-color);
}
