# MiChat 系统架构文档

## 1. 系统概述

### 1.1 设计目标
- 构建高性能、安全可靠的政企级桌面应用
- 提供流畅的用户体验和稳定的系统性能
- 支持跨平台部署和自动更新
- 确保数据安全和隐私保护

### 1.2 技术选型
- **核心框架**: Electron
- **开发语言**: JavaScript/TypeScript
- **构建工具**: electron-builder
- **包管理**: npm
- **版本控制**: Git
- **日志系统**: electron-log
- **自动更新**: electron-updater

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TD
    A[MiChat] --> B[主进程 main.js]
    A --> C[渲染进程]
    A --> D[预加载脚本 preload.js]
    
    B --> E[窗口管理]
    B --> F[自动更新]
    B --> G[缓存管理]
    B --> H[错误处理]
    
    C --> I[src/chat]
    C --> J[src/agent]
    
    I --> K[聊天界面]
    I --> L[消息处理]
    
    J --> M[代理功能]
    J --> N[业务处理]
    
    D --> O[API 暴露]
    D --> P[安全隔离]
    
    E --> Q[窗口创建]
    E --> R[窗口配置]
    
    F --> S[更新检查]
    F --> T[更新下载]
    
    G --> U[缓存清理]
    G --> V[会话管理]
    
    H --> W[异常捕获]
    H --> X[错误提示]
```

### 2.2 进程架构

#### 2.2.1 主进程 (Main Process)
- **职责**：
  - 应用生命周期管理
  - 窗口管理
  - 系统级功能实现
  - 进程间通信协调

- **核心模块**：
  ```javascript
  // 窗口管理模块
  class WindowManager {
    createWindow() { /* 创建主窗口 */ }
    configureWindow() { /* 配置窗口属性 */ }
    handleWindowEvents() { /* 处理窗口事件 */ }
  }

  // 更新管理模块
  class UpdateManager {
    checkForUpdates() { /* 检查更新 */ }
    downloadUpdate() { /* 下载更新 */ }
    installUpdate() { /* 安装更新 */ }
  }

  // 缓存管理模块
  class CacheManager {
    clearCache() { /* 清理缓存 */ }
    manageSession() { /* 管理会话 */ }
    optimizeStorage() { /* 优化存储 */ }
  }
  ```

#### 2.2.2 渲染进程 (Renderer Process)
- **职责**：
  - 用户界面渲染
  - 用户交互处理
  - 业务逻辑实现

- **核心模块**：
  ```javascript
  // 聊天模块
  class ChatModule {
    renderUI() { /* 渲染聊天界面 */ }
    handleMessages() { /* 处理消息 */ }
    manageHistory() { /* 管理历史记录 */ }
  }

  // 代理模块
  class AgentModule {
    handleProxy() { /* 处理代理请求 */ }
    processBusiness() { /* 处理业务逻辑 */ }
    transformData() { /* 数据转换 */ }
  }
  ```

#### 2.2.3 预加载脚本 (Preload Script)
- **职责**：
  - 安全地暴露主进程功能
  - 实现进程间通信
  - 提供API接口

- **实现示例**：
  ```javascript
  // API暴露
  contextBridge.exposeInMainWorld('api', {
    sendMessage: (message) => ipcRenderer.send('send-message', message),
    receiveMessage: (callback) => ipcRenderer.on('receive-message', callback),
    updateApp: () => ipcRenderer.send('check-updates')
  });
  ```

## 3. 核心功能实现

### 3.1 窗口管理
- **窗口配置**：
  ```javascript
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });
  ```

- **窗口事件处理**：
  ```javascript
  mainWindow.on('ready-to-show', () => {
    mainWindow.show();
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
  ```

### 3.2 自动更新
- **更新检查**：
  ```javascript
  autoUpdater.checkForUpdates().catch(err => {
    log.error('更新检查失败:', err);
  });
  ```

- **更新下载**：
  ```javascript
  autoUpdater.on('download-progress', (progressObj) => {
    let message = `下载速度: ${progressObj.bytesPerSecond}`;
    message += `\n已下载 ${progressObj.percent}%`;
    log.info(message);
  });
  ```

### 3.3 缓存管理
- **缓存清理**：
  ```javascript
  const clearCache = async () => {
    const ses = mainWindow.webContents.session;
    await ses.clearCache();
    await ses.clearStorageData();
  };
  ```

- **会话管理**：
  ```javascript
  const manageSession = () => {
    const ses = mainWindow.webContents.session;
    ses.setCachePath(cacheDir);
    ses.setStoragePath(storageDir);
  };
  ```

## 4. 安全机制

### 4.1 进程隔离
- 主进程和渲染进程严格隔离
- 使用 contextBridge 安全地暴露API
- 禁用 nodeIntegration

### 4.2 数据安全
- 敏感数据加密存储
- 安全的进程间通信
- 权限最小化原则

### 4.3 错误处理
- 全局异常捕获
- 优雅的错误恢复
- 详细的错误日志

## 5. 性能优化

### 5.1 资源管理
- 内存使用优化
- 缓存策略优化
- 启动性能优化

### 5.2 渲染优化
- 延迟加载
- 虚拟列表
- 防抖节流

### 5.3 网络优化
- 请求缓存
- 数据压缩
- 断点续传

## 6. 开发指南

### 6.1 环境配置
```bash
# 安装依赖
npm install

# 开发模式
npm start

# 构建应用
npm run build
```

### 6.2 代码规范
- 使用 ESLint 进行代码检查
- 遵循项目既定的代码规范
- 使用 TypeScript 类型检查

### 6.3 测试策略
- 单元测试覆盖核心功能
- 集成测试验证模块交互
- 端到端测试确保功能完整

### 6.4 发布流程
1. 版本号更新
2. 更新日志编写
3. 代码审查
4. 测试验证
5. 构建打包
6. 发布部署

## 7. 部署说明

### 7.1 开发环境
- Node.js 18+
- npm 9+
- Git

### 7.2 生产环境
- Windows 10+
- macOS 10.13+
- Linux (主流发行版)

### 7.3 构建配置
```javascript
{
  "appId": "com.machaojin.michat",
  "productName": "MiChat",
  "directories": {
    "output": "dist"
  },
  "win": {
    "target": ["nsis"],
    "icon": "build/icons/icon.ico"
  }
}
```

## 8. 维护指南

### 8.1 日志管理
- 应用日志位置：`%APPDATA%/MiChat/logs`
- 日志级别配置
- 日志轮转策略

### 8.2 问题排查
- 常见问题解决方案
- 调试工具使用
- 性能问题诊断

### 8.3 更新维护
- 版本更新流程
- 数据迁移方案
- 回滚机制 