/* 设置面板样式 */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.settings-content {
    background: var(--bg-color);
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.settings-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
}

.close-button {
    font-size: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
}

.settings-body {
    padding: 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.settings-item {
    margin-bottom: 1.25rem;
    display: flex;
    flex-direction: column;
}

.settings-item label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.settings-item input[type="text"],
.settings-item input[type="password"],
.settings-item input[type="number"] {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.95rem;
    background: var(--input-bg);
    color: var(--text-color);
}

.settings-item input[type="text"]:focus,
.settings-item input[type="password"]:focus,
.settings-item input[type="number"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.settings-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.save-button,
.cancel-button {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-button {
    background: var(--primary-color);
    color: white;
    border: none;
}

.save-button:hover {
    background: #3a80d2;
    transform: translateY(-1px);
}

.cancel-button {
    background: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.cancel-button:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 思考轮次标记样式 */
.message-content strong {
    color: var(--primary-color);
}
.app-list {
    background: var(--bg-color, #fff);
    border-radius: 12px;
    padding: 16px;
    margin-top: 20px;
}

.app-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color, #eee);
}

.app-list-header h3 {
    margin: 0;
    color: var(--text-color, #333);
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-count {
    color: var(--secondary-text-color, #666);
    font-size: 14px;
}

.app-list-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.app-item {
    background: var(--card-bg-color, #f8f9fa);
    border: 1px solid var(--border-color, #eee);
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.app-item:hover, .app-item.hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color, #4a90e2);
}

.app-item.selected {
    border-color: var(--primary-color, #4a90e2);
    background: var(--primary-color-light, #f0f7ff);
}

.app-item-content {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.app-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--primary-color, #4a90e2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.app-info {
    flex: 1;
    min-width: 0;
}

.app-name {
    margin: 0 0 4px 0;
    color: var(--text-color, #333);
    font-size: 16px;
    font-weight: 600;
}

.app-url {
    color: var(--secondary-text-color, #666);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.app-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: var(--primary-color, #4a90e2);
    color: white;
}

.app-badge.secondary {
    background: var(--secondary-color, #6c757d);
}

.app-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.app-item:hover .app-actions {
    opacity: 1;
}

.app-select-btn {
    background: transparent;
    border: none;
    color: var(--primary-color, #4a90e2);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.app-select-btn:hover {
    background: var(--primary-color-light, #f0f7ff);
}

.main-app .app-icon {
    background: var(--accent-color, #f59e0b);
}