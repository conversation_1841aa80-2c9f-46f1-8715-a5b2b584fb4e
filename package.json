{"_comment_project": "MiChat - 基于 Electron 的跨平台智能对话助手", "name": "MiChat", "version": "5.0.2", "description": "小米政企助手 - 基于 Dify API 的智能对话桌面应用", "main": "main.js", "repository": {"type": "git", "url": "https://github.com/machaojin1917939763/chat_plus.git"}, "_comment_scripts": "构建和开发脚本说明", "scripts": {"_comment_dev": "开发相关脚本", "start": "electron .", "_comment_build": "构建相关脚本 - 支持多平台构建", "build": "electron-builder -mwl", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "_comment_publish": "发布相关脚本 - 自动发布到 GitHub Releases", "publish": "electron-builder -mwl -p always", "publish:win": "electron-builder --win -p always", "publish:mac": "electron-builder --mac -p always", "publish:linux": "electron-builder --linux -p always", "_comment_package": "打包相关脚本", "package": "electron-builder --dir", "make": "electron-builder"}, "author": {"name": "Ma Chaojin", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.6.0", "@electron-forge/maker-deb": "^7.6.0", "@electron-forge/maker-rpm": "^7.6.0", "@electron-forge/maker-squirrel": "^7.6.0", "@electron-forge/maker-zip": "^7.6.0", "@electron-forge/plugin-auto-unpack-natives": "^7.6.0", "@electron-forge/plugin-fuses": "^7.6.0", "@electron-forge/publisher-github": "^7.6.0", "@electron/fuses": "^1.8.0", "electron": "^33.2.1", "electron-builder": "^25.1.8"}, "dependencies": {"electron-log": "^5.2.4", "electron-squirrel-startup": "^1.0.1", "electron-updater": "^6.4.0", "update-electron-app": "^3.1.0"}, "build": {"appId": "com.machaojin.michat", "productName": "MiChat", "copyright": "Copyright © 2024 Ma Chaojin", "publish": [{"provider": "github", "owner": "machaojin1917939763", "repo": "chat_plus"}], "directories": {"output": "dist"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icons/icon.ico", "timeStampServer": "http://timestamp.digicert.com"}, "linux": {"target": [{"target": "AppImage"}, {"target": "deb"}], "category": "Development", "icon": "build/icons/icon.png"}, "mac": {"target": [{"target": "dmg"}], "category": "public.app-category.productivity", "icon": "build/icons/icon.icns"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MiChat", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "artifactName": "${productName}-Setup-${version}.${ext}"}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}}}